const express = require("express");
const bodyParser = require("body-parser");
const app = express();
var cors = require('cors')
const db = require("./api/models");
const moment = require('moment-timezone');
const time =  moment.tz( "Asia/Kolkata").hours() + "." + moment.tz( "Asia/Kolkata").minutes()
var os = require("os");
const { startTimeoutSystem } = require("./api/services/cronService");
const http = require('http');
const socketIo = require('socket.io');

app.use(cors({origin: true, credentials: true}));
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));
app.use(express.static('api/uploads')); 

app.get("/", (req, res) => {
  console.log('Application is running', new Date());
  res.json({ message: "Welcome to Sendnow application." });
});
db.sequelize.sync().then(() => {
  console.log("Drop and re-sync db.");
});
require("./api/routes/UserRoute.js")(app);
require("./api/routes/ProfileRoute.js")(app);
require("./api/routes/CategoryRoute.js")(app);
require("./api/routes/ParcelRoute.js")(app);
require("./api/routes/WalletRoute.js")(app);
app.use("/api/location", require("./api/routes/LocationRoute.js"));
// Create HTTP server and Socket.IO instance
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"]
  }
});

// Make io globally available
global.io = io;

// Socket.IO connection handling
io.on('connection', (socket) => {
  console.log('User connected:', socket.id);

  // Join location tracking room
  socket.on('join_location_tracking', (sessionId) => {
    socket.join(`location_${sessionId}`);
    console.log(`User ${socket.id} joined location tracking for session: ${sessionId}`);
  });

  // Leave location tracking room
  socket.on('leave_location_tracking', (sessionId) => {
    socket.leave(`location_${sessionId}`);
    console.log(`User ${socket.id} left location tracking for session: ${sessionId}`);
  });

  // Handle location updates
  socket.on('location_update', (data) => {
    // Broadcast location update to all users in the session room
    socket.to(`location_${data.sessionId}`).emit('location_update', data);
  });

  socket.on('disconnect', () => {
    console.log('User disconnected:', socket.id);
  });
});

server.listen(process.env.PORT || 8000, () => {
  const port = server.address().port;
  console.log(`Server is working on port ${port} ${JSON.stringify(server.address())}`);

  // Start immediate timeout system after server starts
  startTimeoutSystem();
});