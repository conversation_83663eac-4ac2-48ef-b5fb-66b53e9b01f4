# For HTTP
<IfModule mod_proxy.c>
  # Serve Let's Encrypt challenge files directly
  Alias /.well-known/ "/var/www/html/.well-known/"
  <Directory "/var/www/html/.well-known/">
      AllowOverride None
      Require all granted
  </Directory>

  ProxyPreserveHost On
  # Exclude .well-known from proxy
  ProxyPassMatch ^/.well-known/acme-challenge/ !

  # Proxy everything else to Node.js
  ProxyPass / http://127.0.0.1:8000/
  ProxyPassReverse / http://127.0.0.1:8000/

  # Allow large file uploads (100MB = 104857600 bytes)
  LimitRequestBody 104857600

  # Increase proxy timeout for large uploads
  ProxyTimeout 2400

  # Optional: Adjust buffering for large requests
  ProxyIOBufferSize 8192
</IfModule>

# For HTTPS
<IfModule mod_ssl.c>
<VirtualHost *:443>
  ServerName sendnow.app
  ServerAlias www.sendnow.app

  ProxyPreserveHost On

  # Bypass Let's Encrypt challenges
  ProxyPassMatch ^/.well-known/acme-challenge/ !

  # Proxy everything else to Node.js
  ProxyPass / http://127.0.0.1:8000/
  ProxyPassReverse / http://127.0.0.1:8000/

  # Allow large file uploads (100MB = 104857600 bytes)
  LimitRequestBody 104857600

  # Increase proxy timeout for large uploads
  ProxyTimeout 2400

  # Optional: Adjust buffering for large requests
  ProxyIOBufferSize 8192

  # SSL Certs from Let's Encrypt
  SSLEngine on
  SSLCertificateFile /etc/letsencrypt/live/sendnow.app/fullchain.pem
  SSLCertificateKeyFile /etc/letsencrypt/live/sendnow.app/privkey.pem

  # Optional: serve challenge files
  Alias /.well-known/ "/var/www/html/.well-known/"
  <Directory "/var/www/html/.well-known/">
    AllowOverride None
    Require all granted
  </Directory>
</VirtualHost>
</IfModule>

# Global timeout for large uploads (affects both HTTP and HTTPS)
Timeout 2400