"use strict";

module.exports = function (sequelize, DataTypes) {
  const LocationTrackingModel = sequelize.define(
    "LocationTrackingModel",
    {
      id: {
        type: DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true,
      },
      userId: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: "users",
          key: "id",
        },
        comment: "User whose location is being tracked",
      },
      sessionId: {
        type: DataTypes.STRING,
        allowNull: false,
        comment: "Unique session identifier for tracking",
      },
      parcelId: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: "parcels",
          key: "id",
        },
        comment: "Associated parcel ID if tracking for delivery",
      },
      latitude: {
        type: DataTypes.DECIMAL(10, 7),
        allowNull: false,
        comment: "Current latitude coordinate",
      },
      longitude: {
        type: DataTypes.DECIMAL(10, 7),
        allowNull: false,
        comment: "Current longitude coordinate",
      },
      accuracy: {
        type: DataTypes.DECIMAL(8, 2),
        allowNull: true,
        comment: "GPS accuracy in meters",
      },
      speed: {
        type: DataTypes.DECIMAL(8, 2),
        allowNull: true,
        comment: "Current speed in m/s",
      },
      heading: {
        type: DataTypes.DECIMAL(8, 2),
        allowNull: true,
        comment: "Direction of movement in degrees",
      },
      altitude: {
        type: DataTypes.DECIMAL(8, 2),
        allowNull: true,
        comment: "Altitude in meters",
      },
      isActive: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: true,
        comment: "Whether tracking is currently active",
      },
      trackingType: {
        type: DataTypes.ENUM("parcel_delivery", "location_sharing", "general"),
        allowNull: false,
        defaultValue: "general",
        comment: "Type of location tracking",
      },
      deviceInfo: {
        type: DataTypes.JSON,
        allowNull: true,
        comment: "Device information (battery, network, etc.)",
      },
      createdAt: {
        field: "created_at",
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
      },
      updatedAt: {
        field: "updated_at",
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
      },
    },
    {
      tableName: "location_tracking",
      indexes: [
        {
          fields: ["userId", "sessionId"],
        },
        {
          fields: ["parcelId"],
        },
        {
          fields: ["isActive"],
        },
        {
          fields: ["created_at"],
        },
      ],
    }
  );

  LocationTrackingModel.associate = function (models) {
    LocationTrackingModel.belongsTo(models.UserModel, {
      foreignKey: "userId",
      as: "user",
    });

    LocationTrackingModel.belongsTo(models.ParcelModel, {
      foreignKey: "parcelId",
      as: "parcel",
    });
  };

  return LocationTrackingModel;
};
