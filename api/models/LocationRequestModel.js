"use strict";

module.exports = function (sequelize, DataTypes) {
  const LocationRequestModel = sequelize.define(
    "LocationRequestModel",
    {
      id: {
        type: DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true,
      },
      requestId: {
        type: DataTypes.STRING,
        allowNull: false,
        unique: true,
        comment: "Unique identifier for the location request",
      },
      fromUserId: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: "users",
          key: "id",
        },
        comment: "User requesting location access",
      },
      toUserId: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: "users",
          key: "id",
        },
        comment: "User whose location is being requested",
      },
      message: {
        type: DataTypes.TEXT,
        allowNull: true,
        comment: "Optional message with the request",
      },
      status: {
        type: DataTypes.ENUM("pending", "accepted", "declined", "expired"),
        allowNull: false,
        defaultValue: "pending",
        comment: "Status of the location request",
      },
      requestType: {
        type: DataTypes.ENUM("one_time", "continuous", "parcel_delivery"),
        allowNull: false,
        defaultValue: "one_time",
        comment: "Type of location access requested",
      },
      duration: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: "Requested duration in minutes (null for indefinite)",
      },
      expiresAt: {
        type: DataTypes.DATE,
        allowNull: true,
        comment: "When the request expires if not responded to",
      },
      respondedAt: {
        type: DataTypes.DATE,
        allowNull: true,
        comment: "When the request was responded to",
      },
      shareId: {
        type: DataTypes.STRING,
        allowNull: true,
        comment: "Generated share ID if request is accepted",
      },
      createdAt: {
        field: "created_at",
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
      },
      updatedAt: {
        field: "updated_at",
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
      },
    },
    {
      tableName: "location_requests",
      indexes: [
        {
          fields: ["requestId"],
          unique: true,
        },
        {
          fields: ["fromUserId"],
        },
        {
          fields: ["toUserId"],
        },
        {
          fields: ["status"],
        },
        {
          fields: ["expiresAt"],
        },
      ],
    }
  );

  LocationRequestModel.associate = function (models) {
    LocationRequestModel.belongsTo(models.UserModel, {
      foreignKey: "fromUserId",
      as: "fromUser",
    });

    LocationRequestModel.belongsTo(models.UserModel, {
      foreignKey: "toUserId",
      as: "toUser",
    });
  };

  return LocationRequestModel;
};
