"use strict";

module.exports = function (sequelize, DataTypes) {
  const LocationSharingModel = sequelize.define(
    "LocationSharingModel",
    {
      id: {
        type: DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true,
      },
      shareId: {
        type: DataTypes.STRING,
        allowNull: false,
        unique: true,
        comment: "Unique identifier for the sharing session",
      },
      fromUserId: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: "users",
          key: "id",
        },
        comment: "User who is sharing their location",
      },
      toUserId: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: "users",
          key: "id",
        },
        comment: "User who can view the shared location (null for public sharing)",
      },
      sessionId: {
        type: DataTypes.STRING,
        allowNull: false,
        comment: "Associated location tracking session ID",
      },
      shareUrl: {
        type: DataTypes.STRING,
        allowNull: true,
        comment: "Public URL for location sharing",
      },
      title: {
        type: DataTypes.STRING,
        allowNull: true,
        comment: "Title for the shared location",
      },
      message: {
        type: DataTypes.TEXT,
        allowNull: true,
        comment: "Optional message with the shared location",
      },
      isActive: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: true,
        comment: "Whether sharing is currently active",
      },
      isPublic: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
        comment: "Whether the location can be viewed by anyone with the link",
      },
      expiresAt: {
        type: DataTypes.DATE,
        allowNull: true,
        comment: "When the sharing expires (null for no expiration)",
      },
      viewCount: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: "Number of times the location has been viewed",
      },
      maxViews: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: "Maximum number of views allowed (null for unlimited)",
      },
      settings: {
        type: DataTypes.JSON,
        allowNull: true,
        comment: "Additional sharing settings (accuracy level, update frequency, etc.)",
      },
      createdAt: {
        field: "created_at",
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
      },
      updatedAt: {
        field: "updated_at",
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
      },
      endedAt: {
        field: "ended_at",
        type: DataTypes.DATE,
        allowNull: true,
        comment: "When the sharing was manually ended",
      },
    },
    {
      tableName: "location_sharing",
      indexes: [
        {
          fields: ["shareId"],
          unique: true,
        },
        {
          fields: ["fromUserId"],
        },
        {
          fields: ["toUserId"],
        },
        {
          fields: ["sessionId"],
        },
        {
          fields: ["isActive"],
        },
        {
          fields: ["expiresAt"],
        },
      ],
    }
  );

  LocationSharingModel.associate = function (models) {
    LocationSharingModel.belongsTo(models.UserModel, {
      foreignKey: "fromUserId",
      as: "fromUser",
    });

    LocationSharingModel.belongsTo(models.UserModel, {
      foreignKey: "toUserId",
      as: "toUser",
    });
  };

  return LocationSharingModel;
};
