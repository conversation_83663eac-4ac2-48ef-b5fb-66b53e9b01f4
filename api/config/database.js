const Constants = require('./Constants');

const config = Constants.is_local ? {
  host: 'localhost',
  port: '3306',
  username: "username",
  password: 'password',
  database: "sendnow",
  logging: false,
  dialect: "mysql",
  pool: {
    max: 5,
    min: 0,
    acquire: 30000,
    idle: 10000
  }
} : {
  host: '**************',
  port: '3306',
  username: "sendnowapp_hiren",
  password: '<PERSON><PERSON>@123',
  database: "sendnowapp_sendnow",
  logging: false,
  dialect: "mysql",
  pool: {
    max: 5,
    min: 0,
    acquire: 30000,
    idle: 10000
  }
};

module.exports = {
  development: config,
  test: config,
  production: config
};
