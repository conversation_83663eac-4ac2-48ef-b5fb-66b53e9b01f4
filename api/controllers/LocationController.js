const db = require("../models");
const LocationTrackingModel = db.locationTracking;
const LocationSharingModel = db.locationSharing;
const LocationRequestModel = db.locationRequest;
const UserModel = db.user;
const ParcelModel = db.parcel;
const ProfileModel = db.profile;
const { Op } = require("sequelize");
const { v4: uuidv4 } = require('uuid');

// Start location tracking session
exports.startLocationTracking = async (req, res) => {
  try {
    console.log("Starting location tracking");
    const { sessionId, parcelId, trackingType = "general" } = req.body;
    console.log("Starting location tracking", req.body);
    const userId = req.userId;

    // Validate required fields
    if (!sessionId) {
      return res.status(400).json({
        status: false,
        message: "Session ID is required",
      });
    }

    // Check if session already exists and is active
    const existingSession = await LocationTrackingModel.findOne({
      where: {
        userId,
        sessionId,
        isActive: true,
      },
    });

    if (existingSession) {
      return res.status(200).json({
        status: true,
        message: "Location tracking session already active",
        session: existingSession,
      });
    }

    // Create new tracking session
    const trackingSession = await LocationTrackingModel.create({
      userId,
      sessionId,
      parcelId: parcelId || null,
      trackingType,
      latitude: 0, // Will be updated with first location
      longitude: 0,
      isActive: true,
    });

    res.status(201).json({
      status: true,
      message: "Location tracking started successfully",
      session: trackingSession,
    });
  } catch (error) {
    console.error("Error starting location tracking:", error);
    res.status(500).json({
      status: false,
      message: "Internal server error",
      error: error.message,
    });
  }
};

// Update location during tracking
exports.updateLocation = async (req, res) => {
  try {
    const {
      sessionId,
      latitude,
      longitude,
      accuracy,
      speed,
      heading,
      altitude,
      deviceInfo,
    } = req.body;
    const userId = req.userId;

    // Validate required fields
    if (!sessionId || !latitude || !longitude) {
      return res.status(400).json({
        status: false,
        message: "Session ID, latitude, and longitude are required",
      });
    }

    // Find active tracking session
    const trackingSession = await LocationTrackingModel.findOne({
      where: {
        userId,
        sessionId,
        isActive: true,
      },
    });

    if (!trackingSession) {
      return res.status(404).json({
        status: false,
        message: "Active tracking session not found",
      });
    }

    // Update location
    await trackingSession.update({
      latitude,
      longitude,
      accuracy: accuracy || null,
      speed: speed || null,
      heading: heading || null,
      altitude: altitude || null,
      deviceInfo: deviceInfo || null,
      updatedAt: new Date(),
    });

    // Emit real-time update via WebSocket (if implemented)
    if (global.io) {
      global.io.emit(`location_update_${sessionId}`, {
        userId,
        sessionId,
        latitude,
        longitude,
        accuracy,
        speed,
        heading,
        timestamp: new Date(),
      });
    }

    res.status(200).json({
      status: true,
      message: "Location updated successfully",
      location: {
        latitude,
        longitude,
        accuracy,
        speed,
        heading,
        timestamp: new Date(),
      },
    });
  } catch (error) {
    console.error("Error updating location:", error);
    res.status(500).json({
      status: false,
      message: "Internal server error",
      error: error.message,
    });
  }
};

// Stop location tracking
exports.stopLocationTracking = async (req, res) => {
  try {
    const { sessionId } = req.body;
    const userId = req.userId;

    if (!sessionId) {
      return res.status(400).json({
        status: false,
        message: "Session ID is required",
      });
    }

    // Find and deactivate tracking session
    const trackingSession = await LocationTrackingModel.findOne({
      where: {
        userId,
        sessionId,
        isActive: true,
      },
    });

    if (!trackingSession) {
      return res.status(404).json({
        status: false,
        message: "Active tracking session not found",
      });
    }

    await trackingSession.update({
      isActive: false,
      updatedAt: new Date(),
    });

    // Also deactivate any related location sharing
    await LocationSharingModel.update(
      {
        isActive: false,
        endedAt: new Date(),
      },
      {
        where: {
          sessionId,
          isActive: true,
        },
      }
    );

    res.status(200).json({
      status: true,
      message: "Location tracking stopped successfully",
    });
  } catch (error) {
    console.error("Error stopping location tracking:", error);
    res.status(500).json({
      status: false,
      message: "Internal server error",
      error: error.message,
    });
  }
};

// Get current location for a session
exports.getCurrentLocation = async (req, res) => {
  try {
    const { sessionId } = req.params;

    const trackingSession = await LocationTrackingModel.findOne({
      where: {
        sessionId,
        isActive: true,
      },
      include: [
        {
          model: UserModel,
          as: "user",
          include: [
            {
              model: ProfileModel,
              as: "profile",
            },
          ],
        },
      ],
      order: [["updatedAt", "DESC"]],
    });

    if (!trackingSession) {
      return res.status(404).json({
        status: false,
        message: "Active tracking session not found",
      });
    }

    res.status(200).json({
      status: true,
      location: {
        userId: trackingSession.userId,
        userName: trackingSession.user?.profile?.name || "Unknown",
        sessionId: trackingSession.sessionId,
        latitude: trackingSession.latitude,
        longitude: trackingSession.longitude,
        accuracy: trackingSession.accuracy,
        speed: trackingSession.speed,
        heading: trackingSession.heading,
        lastUpdated: trackingSession.updatedAt,
      },
    });
  } catch (error) {
    console.error("Error getting current location:", error);
    res.status(500).json({
      status: false,
      message: "Internal server error",
      error: error.message,
    });
  }
};

// Get location history for a session
exports.getLocationHistory = async (req, res) => {
  try {
    const { sessionId } = req.params;
    const { limit = 50, offset = 0 } = req.query;

    const locations = await LocationTrackingModel.findAll({
      where: {
        sessionId,
      },
      order: [["createdAt", "DESC"]],
      limit: parseInt(limit),
      offset: parseInt(offset),
      include: [
        {
          model: UserModel,
          as: "user",
          include: [
            {
              model: ProfileModel,
              as: "profile",
            },
          ],
        },
      ],
    });

    res.status(200).json({
      status: true,
      locations: locations.map(loc => ({
        latitude: loc.latitude,
        longitude: loc.longitude,
        accuracy: loc.accuracy,
        speed: loc.speed,
        heading: loc.heading,
        timestamp: loc.createdAt,
      })),
      total: locations.length,
    });
  } catch (error) {
    console.error("Error getting location history:", error);
    res.status(500).json({
      status: false,
      message: "Internal server error",
      error: error.message,
    });
  }
};

// Start location sharing
exports.startLocationSharing = async (req, res) => {
  try {
    const {
      sessionId,
      toUserId,
      title,
      message,
      isPublic = false,
      expiresAt,
      maxViews,
    } = req.body;
    const fromUserId = req.userId;

    if (!sessionId) {
      return res.status(400).json({
        status: false,
        message: "Session ID is required",
      });
    }

    // Verify tracking session exists and is active
    const trackingSession = await LocationTrackingModel.findOne({
      where: {
        userId: fromUserId,
        sessionId,
        isActive: true,
      },
    });

    if (!trackingSession) {
      return res.status(404).json({
        status: false,
        message: "Active tracking session not found",
      });
    }

    // Generate unique share ID
    const shareId = uuidv4();
    const shareUrl = `${process.env.FRONTEND_URL || 'http://localhost:3000'}/shared-location/${shareId}`;

    // Create location sharing record
    const locationShare = await LocationSharingModel.create({
      shareId,
      fromUserId,
      toUserId: toUserId || null,
      sessionId,
      shareUrl,
      title: title || null,
      message: message || null,
      isPublic,
      expiresAt: expiresAt ? new Date(expiresAt) : null,
      maxViews: maxViews || null,
      isActive: true,
    });

    res.status(201).json({
      status: true,
      message: "Location sharing started successfully",
      share: {
        shareId,
        shareUrl,
        expiresAt: locationShare.expiresAt,
        isPublic,
      },
    });
  } catch (error) {
    console.error("Error starting location sharing:", error);
    res.status(500).json({
      status: false,
      message: "Internal server error",
      error: error.message,
    });
  }
};

// Stop location sharing
exports.stopLocationSharing = async (req, res) => {
  try {
    const { shareId } = req.body;
    const userId = req.userId;

    if (!shareId) {
      return res.status(400).json({
        status: false,
        message: "Share ID is required",
      });
    }

    // Find and deactivate sharing
    const locationShare = await LocationSharingModel.findOne({
      where: {
        shareId,
        fromUserId: userId,
        isActive: true,
      },
    });

    if (!locationShare) {
      return res.status(404).json({
        status: false,
        message: "Active location sharing not found",
      });
    }

    await locationShare.update({
      isActive: false,
      endedAt: new Date(),
    });

    res.status(200).json({
      status: true,
      message: "Location sharing stopped successfully",
    });
  } catch (error) {
    console.error("Error stopping location sharing:", error);
    res.status(500).json({
      status: false,
      message: "Internal server error",
      error: error.message,
    });
  }
};

// Get shared location (public access)
exports.getSharedLocation = async (req, res) => {
  try {
    const { shareId } = req.params;

    // Find active sharing
    const locationShare = await LocationSharingModel.findOne({
      where: {
        shareId,
        isActive: true,
        [Op.or]: [
          { expiresAt: null },
          { expiresAt: { [Op.gt]: new Date() } },
        ],
      },
      include: [
        {
          model: UserModel,
          as: "fromUser",
          include: [
            {
              model: ProfileModel,
              as: "profile",
            },
          ],
        },
      ],
    });

    if (!locationShare) {
      return res.status(404).json({
        status: false,
        message: "Shared location not found or expired",
      });
    }

    // Check view limits
    if (locationShare.maxViews && locationShare.viewCount >= locationShare.maxViews) {
      return res.status(403).json({
        status: false,
        message: "Maximum views exceeded",
      });
    }

    // Get current location from tracking session
    const currentLocation = await LocationTrackingModel.findOne({
      where: {
        sessionId: locationShare.sessionId,
        isActive: true,
      },
      order: [["updatedAt", "DESC"]],
    });

    if (!currentLocation) {
      return res.status(404).json({
        status: false,
        message: "Current location not available",
      });
    }

    // Increment view count
    await locationShare.increment('viewCount');

    res.status(200).json({
      status: true,
      share: {
        shareId: locationShare.shareId,
        title: locationShare.title,
        message: locationShare.message,
        fromUser: {
          name: locationShare.fromUser?.profile?.name || "Unknown",
        },
        location: {
          latitude: currentLocation.latitude,
          longitude: currentLocation.longitude,
          accuracy: currentLocation.accuracy,
          lastUpdated: currentLocation.updatedAt,
        },
        createdAt: locationShare.createdAt,
        expiresAt: locationShare.expiresAt,
      },
    });
  } catch (error) {
    console.error("Error getting shared location:", error);
    res.status(500).json({
      status: false,
      message: "Internal server error",
      error: error.message,
    });
  }
};

// Request location access from another user
exports.requestLocationAccess = async (req, res) => {
  try {
    const { toUserId, message, requestType = "one_time", duration } = req.body;
    const fromUserId = req.userId;

    if (!toUserId) {
      return res.status(400).json({
        status: false,
        message: "Target user ID is required",
      });
    }

    if (fromUserId === toUserId) {
      return res.status(400).json({
        status: false,
        message: "Cannot request location access from yourself",
      });
    }

    // Check if user exists
    const targetUser = await UserModel.findByPk(toUserId);
    if (!targetUser) {
      return res.status(404).json({
        status: false,
        message: "Target user not found",
      });
    }

    // Generate unique request ID
    const requestId = uuidv4();

    // Set expiration (24 hours from now)
    const expiresAt = new Date();
    expiresAt.setHours(expiresAt.getHours() + 24);

    // Create location request
    const locationRequest = await LocationRequestModel.create({
      requestId,
      fromUserId,
      toUserId,
      message: message || null,
      requestType,
      duration: duration || null,
      expiresAt,
      status: "pending",
    });

    // TODO: Send push notification to target user

    res.status(201).json({
      status: true,
      message: "Location access request sent successfully",
      request: {
        requestId,
        status: "pending",
        expiresAt,
      },
    });
  } catch (error) {
    console.error("Error requesting location access:", error);
    res.status(500).json({
      status: false,
      message: "Internal server error",
      error: error.message,
    });
  }
};

// Respond to location access request
exports.respondToLocationRequest = async (req, res) => {
  try {
    const { requestId, response } = req.body; // response: 'accepted' or 'declined'
    const userId = req.userId;

    if (!requestId || !response) {
      return res.status(400).json({
        status: false,
        message: "Request ID and response are required",
      });
    }

    if (!["accepted", "declined"].includes(response)) {
      return res.status(400).json({
        status: false,
        message: "Response must be 'accepted' or 'declined'",
      });
    }

    // Find pending request
    const locationRequest = await LocationRequestModel.findOne({
      where: {
        requestId,
        toUserId: userId,
        status: "pending",
        expiresAt: { [Op.gt]: new Date() },
      },
    });

    if (!locationRequest) {
      return res.status(404).json({
        status: false,
        message: "Location request not found or expired",
      });
    }

    // Update request status
    await locationRequest.update({
      status: response,
      respondedAt: new Date(),
    });

    let shareId = null;
    if (response === "accepted") {
      // Create location sharing if accepted
      shareId = uuidv4();
      const shareUrl = `${process.env.FRONTEND_URL || 'http://localhost:3000'}/shared-location/${shareId}`;

      // Set expiration based on request duration
      let expiresAt = null;
      if (locationRequest.duration) {
        expiresAt = new Date();
        expiresAt.setMinutes(expiresAt.getMinutes() + locationRequest.duration);
      }

      await LocationSharingModel.create({
        shareId,
        fromUserId: userId,
        toUserId: locationRequest.fromUserId,
        sessionId: `request_${requestId}`, // Temporary session ID
        shareUrl,
        title: "Location Access Request",
        isPublic: false,
        expiresAt,
        isActive: true,
      });

      // Update request with share ID
      await locationRequest.update({ shareId });
    }

    res.status(200).json({
      status: true,
      message: `Location request ${response} successfully`,
      shareId: shareId,
    });
  } catch (error) {
    console.error("Error responding to location request:", error);
    res.status(500).json({
      status: false,
      message: "Internal server error",
      error: error.message,
    });
  }
};

// Get user's location requests (received)
exports.getLocationRequests = async (req, res) => {
  try {
    const userId = req.userId;
    const { status } = req.query;

    const whereClause = { toUserId: userId };
    if (status) {
      whereClause.status = status;
    }

    const requests = await LocationRequestModel.findAll({
      where: whereClause,
      include: [
        {
          model: UserModel,
          as: "fromUser",
          include: [
            {
              model: ProfileModel,
              as: "profile",
            },
          ],
        },
      ],
      order: [["createdAt", "DESC"]],
    });

    res.status(200).json({
      status: true,
      requests: requests.map(req => ({
        requestId: req.requestId,
        fromUser: {
          id: req.fromUserId,
          name: req.fromUser?.profile?.name || "Unknown",
        },
        message: req.message,
        requestType: req.requestType,
        duration: req.duration,
        status: req.status,
        createdAt: req.createdAt,
        expiresAt: req.expiresAt,
        respondedAt: req.respondedAt,
      })),
    });
  } catch (error) {
    console.error("Error getting location requests:", error);
    res.status(500).json({
      status: false,
      message: "Internal server error",
      error: error.message,
    });
  }
};

// Get user's shared locations
exports.getUserSharedLocations = async (req, res) => {
  try {
    const userId = req.userId;

    const sharedLocations = await LocationSharingModel.findAll({
      where: {
        fromUserId: userId,
        isActive: true,
      },
      include: [
        {
          model: UserModel,
          as: "toUser",
          include: [
            {
              model: ProfileModel,
              as: "profile",
            },
          ],
        },
      ],
      order: [["createdAt", "DESC"]],
    });

    res.status(200).json({
      status: true,
      sharedLocations: sharedLocations.map(share => ({
        shareId: share.shareId,
        shareUrl: share.shareUrl,
        title: share.title,
        toUser: share.toUser ? {
          id: share.toUserId,
          name: share.toUser?.profile?.name || "Unknown",
        } : null,
        isPublic: share.isPublic,
        viewCount: share.viewCount,
        createdAt: share.createdAt,
        expiresAt: share.expiresAt,
      })),
    });
  } catch (error) {
    console.error("Error getting user shared locations:", error);
    res.status(500).json({
      status: false,
      message: "Internal server error",
      error: error.message,
    });
  }
};
