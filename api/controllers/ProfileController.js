var md5 = require("md5");
const db = require("../models");
const User = db.user;
const Profile = db.profile;
var jwt = require("jsonwebtoken");
const sendmail = require("../utils/Sendmail.js");
const formidable = require("formidable");
const fs = require("fs");
const Constants = require("../config/Constants.js");

exports.getProfile = async (req, res) => {
  try {
    console.log(`Fetching profile for userId: ${req.userId}`);
    const data = await User.findAll({
      where: {
        id: req.userId,
      },
      include: [
        {
          model: Profile,
          as: "profile",
          rejectOnEmpty: true,
          required: false,
        },
      ],
    });
    console.log(`Profile fetch result: ${data.length} records found`);

    if (data.length != 0) {
      const userData = data[0];
      delete userData.dataValues.password;
      console.log(`Generating JWT token for userId: ${data[0].id}`);
      const token = jwt.sign({ id: data[0].id }, Constants.jwt_token_secret, {});
      const newObject = Object.assign(userData.dataValues);
      if (newObject.profile?.profile_pic_hash) {
        newObject.imageUrl =
          req.protocol +
          "://" +
          req.get("host") +
          "/" +
          userData.dataValues.profile.profile_pic_hash +
          "." +
          userData.dataValues.profile.profile_pic_ext;
        console.log(`Image URL generated: ${newObject.imageUrl}`);
      } else {
        newObject.imageUrl = "";
        console.log("No profile picture found, setting empty imageUrl");
      }
      const dataObject = {
        status: true,
        message: "Profile get successfully!",
        token: token,
        userData: newObject,
      };
      console.log("Sending successful profile response");
      res.send(dataObject);
    } else {
      console.log("No user found with provided credentials");
      res.status(400).send({
        status: false,
        message: "Email or Password not Match!",
      });
    }
  } catch (err) {
    console.error(`Error fetching profile for userId: ${req.userId}`, err);
    res.status(500).send({
      status: false,
      message: err.message || "Something went wrong.",
    });
  }
};

exports.updateProfile = async (req, res) => {
  try {
    console.log(`Starting profile update for userId: ${req.userId}`);
    const form = new formidable.IncomingForm({
      hashAlgorithm: "sha1",
    });
    form.parse(req, async function (err, fields, files) {
      try {
        if (err) {
          console.error("Error parsing form data", err);
          throw err;
        }
        console.log("Form data parsed successfully", { fields, files });

        Object.keys(fields).forEach((key) => {
          if (Array.isArray(fields[key]) && fields[key].length === 1) {
            fields[key] = fields[key][0]; // Extract string from array
            console.log(`Converted field ${key} from array to string`);
          }
        });

        if (!!files.file && files.file.length > 0) {
          console.log("Processing file upload");
          const oldPath = files.file[0].filepath;
          const newPath =
            __dirname + "/../uploads" + "/" + files.file[0].hash + ".png";
          try {
            const rawData = fs.readFileSync(oldPath);
            console.log(`Read file from ${oldPath}`);
            fs.writeFile(newPath, rawData, async function (err) {
              try {
                if (err) {
                  console.error(`Error writing file to ${newPath}`, err);
                  throw err;
                }
                console.log(`File written successfully to ${newPath}`);

                const userUpdate = {
                  name: fields.name,
                };
                const profileUpdate = {
                  profile_pic_hash: files.file[0].hash,
                  profile_pic_ext: "png",
                  gender: fields.gender,
                  phone_number: fields.phone_number
                };
                if (fields.date_of_birth) {
                  const parsedDate = new Date(fields.date_of_birth);
                  profileUpdate.date_of_birth = parsedDate;
                  console.log(`Parsed date of birth: ${parsedDate}`);
                }
                await User.update(userUpdate, {
                  where: { id: req.userId },
                });
                console.log(`User updated for userId: ${req.userId}`);

                await Profile.update(profileUpdate, {
                  where: { userId: req.userId },
                });
                console.log(`Profile updated for userId: ${req.userId}`);

                const data = await User.findOne({
                  where: { id: req.userId },
                  include: [
                    {
                      model: Profile,
                      as: "profile",
                      rejectOnEmpty: true,
                      required: false,
                    },
                  ],
                });
                console.log(`Fetched updated user data for userId: ${req.userId}`);

                const userData = data;
                console.log(req.userId, 'userDatauserData');
                delete userData.dataValues.password;
                const token = jwt.sign({ id: data.id }, Constants.jwt_token_secret, {});
                console.log(`Generated new JWT token for userId: ${data.id}`);

                const newObject = Object.assign(userData.dataValues);
                if (newObject.profile?.profile_pic_hash) {
                  newObject.imageUrl =
                    req.protocol +
                    "://" +
                    req.get("host") +
                    "/" +
                    userData.dataValues.profile.profile_pic_hash +
                    "." +
                    userData.dataValues.profile.profile_pic_ext;
                  console.log(`Image URL generated: ${newObject.imageUrl}`);
                } else {
                  newObject.imageUrl = "";
                  console.log("No profile picture, setting empty imageUrl");
                }
                const dataObject = {
                  status: true,
                  message: "Profile updated Successfully.",
                  token: token,
                  userData: newObject,
                };
                console.log("Sending successful profile update response");
                res.send(dataObject);
              } catch (err) {
                console.error(`Error in file write or DB update for userId: ${req.userId}`, err);
                res.status(500).send({
                  status: false,
                  message: err.message || "Something went wrong.",
                });
              }
            });
          } catch (err) {
            console.error(`Error reading file from ${oldPath}`, err);
            res.status(500).send({
              status: false,
              message: err.message || "Something went wrong.",
            });
          }
        } else {
          console.log("No file uploaded, updating profile without image");
          const userUpdate = {
            name: fields.name,
          };
          await User.update(userUpdate, {
            where: { id: req.userId },
          });
          console.log(`User updated for userId: ${req.userId}`);

          const profileUpdate = {
            gender: fields.gender,
            phone_number: fields.phone_number
          };
          if (fields.date_of_birth) {
            const parsedDate = new Date(fields.date_of_birth);
            profileUpdate.date_of_birth = parsedDate;
            console.log(`Parsed date of birth: ${parsedDate}`);
          }
          await Profile.update(profileUpdate, {
            where: { userId: req.userId },
          });
          console.log(`Profile updated for userId: ${req.userId}`);

          const data = await User.findOne({
            where: { id: req.userId },
            include: [
              {
                model: Profile,
                as: "profile",
                rejectOnEmpty: true,
                required: false,
              },
            ],
          });
          console.log(`Fetched updated user data for userId: ${req.userId}`);

          const userData = data;
          delete userData.dataValues.password;
          const token = jwt.sign({ id: data.id }, Constants.jwt_token_secret, {});
          console.log(`Generated new JWT token for userId: ${data.id}`);

          const newObject = Object.assign(userData.dataValues);
          if (newObject.profile) {
            newObject.imageUrl =
              req.protocol +
              "://" +
              req.get("host") +
              "/" +
              userData.dataValues.profile.profile_pic_hash +
              "." +
              userData.dataValues.profile.profile_pic_ext;
            console.log(`Image URL generated: ${newObject.imageUrl}`);
          } else {
            newObject.imageUrl = "";
            console.log("No profile picture, setting empty imageUrl");
          }
          const dataObject = {
            status: true,
            message: "Profile updated Successfully.",
            token: token,
            userData: newObject,
          };
          console.log("Sending successful profile update response");
          res.send(dataObject);
        }
      } catch (err) {
        console.error(`Error in form parsing or processing for userId: ${req.userId}`, err);
        res.status(500).send({
          status: false,
          message: err.message || "Something went wrong.",
        });
      }
    });
  } catch (error) {
    console.error(`Error in updateProfile for userId: ${req.userId}`, error);
    res.status(500).send({
      status: false,
      message: error.message || "Something went wrong.",
    });
  }
};