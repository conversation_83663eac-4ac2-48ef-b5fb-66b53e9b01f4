const express = require("express");
const router = express.Router();
const LocationController = require("../controllers/LocationController");
const { verifyToken } = require("../middleware/authJwt");

// Location Tracking Routes
router.post("/tracking/start", verifyToken, LocationController.startLocationTracking);
router.post("/tracking/update", verifyToken, LocationController.updateLocation);
router.post("/tracking/stop", verifyToken, LocationController.stopLocationTracking);
router.get("/tracking/:sessionId/current", LocationController.getCurrentLocation);
router.get("/tracking/:sessionId/history", LocationController.getLocationHistory);

// Location Sharing Routes
router.post("/sharing/start", verifyToken, LocationController.startLocationSharing);
router.post("/sharing/stop", verifyToken, LocationController.stopLocationSharing);
router.get("/sharing/shared/:shareId", LocationController.getSharedLocation);
router.get("/sharing/my-shares", verifyToken, LocationController.getUserSharedLocations);

// Location Request Routes
router.post("/requests/send", verifyToken, LocationController.requestLocationAccess);
router.post("/requests/respond", verifyToken, LocationController.respondToLocationRequest);
router.get("/requests", verifyToken, LocationController.getLocationRequests);

module.exports = router;
