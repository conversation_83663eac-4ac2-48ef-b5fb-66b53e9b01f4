const LocationController = require("../controllers/LocationController");
const { verifyToken } = require("../middleware/authJwt");

module.exports = (app) => {
  // Location Tracking Routes
  app.post(
    "/api/location/tracking/start",
    verifyToken,
    LocationController.startLocationTracking
  );
  app.post(
    "/api/location/tracking/update",
    verifyToken,
    LocationController.updateLocation
  );
  app.post(
    "/api/location/tracking/stop",
    verifyToken,
    LocationController.stopLocationTracking
  );
  app.get(
    "/api/location/tracking/:sessionId/current",
    LocationController.getCurrentLocation
  );
  app.get(
    "/api/location/tracking/:sessionId/history",
    LocationController.getLocationHistory
  );

  // Location Sharing Routes
  app.post(
    "/api/location/sharing/start",
    verifyToken,
    LocationController.startLocationSharing
  );
  app.post(
    "/api/location/sharing/stop",
    verifyToken,
    LocationController.stopLocationSharing
  );
  app.get(
    "/api/location/sharing/shared/:shareId",
    LocationController.getSharedLocation
  );
  app.get(
    "/api/location/sharing/my-shares",
    verifyToken,
    LocationController.getUserSharedLocations
  );

  // Location Request Routes
  app.post(
    "/api/location/requests/send",
    verifyToken,
    LocationController.requestLocationAccess
  );
  app.post(
    "/api/location/requests/respond",
    verifyToken,
    LocationController.respondToLocationRequest
  );
  app.get(
    "/api/location/requests",
    verifyToken,
    LocationController.getLocationRequests
  );
};
