'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn('parcel_acceptance', 'accepted_location_lat', {
      type: Sequelize.DECIMAL(10, 7),
      allowNull: true,
      comment: 'Latitude where parcel was accepted',
    });

    await queryInterface.addColumn('parcel_acceptance', 'accepted_location_lng', {
      type: Sequelize.DECIMAL(10, 7),
      allowNull: true,
      comment: 'Longitude where parcel was accepted',
    });

    await queryInterface.addColumn('parcel_acceptance', 'tracking_session_id', {
      type: Sequelize.STRING,
      allowNull: true,
      comment: 'Location tracking session ID for this delivery',
    });

    // Add index for tracking session ID
    await queryInterface.addIndex('parcel_acceptance', ['tracking_session_id']);
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('parcel_acceptance', 'accepted_location_lat');
    await queryInterface.removeColumn('parcel_acceptance', 'accepted_location_lng');
    await queryInterface.removeColumn('parcel_acceptance', 'tracking_session_id');
  }
};
