'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('location_tracking', {
      id: {
        type: Sequelize.INTEGER,
        autoIncrement: true,
        primaryKey: true,
      },
      userId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      sessionId: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      parcelId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'parcels',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      latitude: {
        type: Sequelize.DECIMAL(10, 7),
        allowNull: false,
      },
      longitude: {
        type: Sequelize.DECIMAL(10, 7),
        allowNull: false,
      },
      accuracy: {
        type: Sequelize.DECIMAL(8, 2),
        allowNull: true,
      },
      speed: {
        type: Sequelize.DECIMAL(8, 2),
        allowNull: true,
      },
      heading: {
        type: Sequelize.DECIMAL(8, 2),
        allowNull: true,
      },
      altitude: {
        type: Sequelize.DECIMAL(8, 2),
        allowNull: true,
      },
      isActive: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: true,
      },
      trackingType: {
        type: Sequelize.ENUM('parcel_delivery', 'location_sharing', 'general'),
        allowNull: false,
        defaultValue: 'general',
      },
      deviceInfo: {
        type: Sequelize.JSON,
        allowNull: true,
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      },
    });

    // Add indexes
    await queryInterface.addIndex('location_tracking', ['userId', 'sessionId']);
    await queryInterface.addIndex('location_tracking', ['parcelId']);
    await queryInterface.addIndex('location_tracking', ['isActive']);
    await queryInterface.addIndex('location_tracking', ['created_at']);
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('location_tracking');
  }
};
