# Location Tracking API Documentation

## Overview
This API provides comprehensive location tracking, sharing, and request functionality for the SendNow application. It includes real-time location updates, location sharing with expiration controls, and location access requests between users.

## Base URL
```
http://localhost:8000/api/location
```

## Authentication
All endpoints (except public sharing endpoints) require JWT authentication via the `Authorization` header:
```
Authorization: Bearer <jwt_token>
```

## WebSocket Connection
Real-time location updates are available via Socket.IO:
```javascript
const socket = io('http://localhost:8000');

// Join location tracking room
socket.emit('join_location_tracking', sessionId);

// Listen for location updates
socket.on('location_update', (data) => {
  console.log('Location update:', data);
});
```

---

## Location Tracking Endpoints

### 1. Start Location Tracking
**POST** `/tracking/start`

Start a new location tracking session.

**Request Body:**
```json
{
  "sessionId": "parcel_123_1640995200000",
  "parcelId": 123,
  "trackingType": "parcel_delivery"
}
```

**Response:**
```json
{
  "status": true,
  "message": "Location tracking started successfully",
  "session": {
    "id": 1,
    "userId": 456,
    "sessionId": "parcel_123_1640995200000",
    "parcelId": 123,
    "trackingType": "parcel_delivery",
    "isActive": true,
    "createdAt": "2024-01-01T10:00:00.000Z"
  }
}
```

### 2. Update Location
**POST** `/tracking/update`

Update current location during an active tracking session.

**Request Body:**
```json
{
  "sessionId": "parcel_123_1640995200000",
  "latitude": 37.7749,
  "longitude": -122.4194,
  "accuracy": 10.5,
  "speed": 5.2,
  "heading": 180.0,
  "altitude": 50.0,
  "deviceInfo": {
    "battery": 85,
    "networkType": "wifi"
  }
}
```

**Response:**
```json
{
  "status": true,
  "message": "Location updated successfully",
  "location": {
    "latitude": 37.7749,
    "longitude": -122.4194,
    "accuracy": 10.5,
    "speed": 5.2,
    "heading": 180.0,
    "timestamp": "2024-01-01T10:05:00.000Z"
  }
}
```

### 3. Stop Location Tracking
**POST** `/tracking/stop`

Stop an active location tracking session.

**Request Body:**
```json
{
  "sessionId": "parcel_123_1640995200000"
}
```

**Response:**
```json
{
  "status": true,
  "message": "Location tracking stopped successfully"
}
```

### 4. Get Current Location
**GET** `/tracking/:sessionId/current`

Get the current location for a tracking session (public endpoint).

**Response:**
```json
{
  "status": true,
  "location": {
    "userId": 456,
    "userName": "John Doe",
    "sessionId": "parcel_123_1640995200000",
    "latitude": 37.7749,
    "longitude": -122.4194,
    "accuracy": 10.5,
    "speed": 5.2,
    "heading": 180.0,
    "lastUpdated": "2024-01-01T10:05:00.000Z"
  }
}
```

### 5. Get Location History
**GET** `/tracking/:sessionId/history?limit=50&offset=0`

Get location history for a tracking session.

**Response:**
```json
{
  "status": true,
  "locations": [
    {
      "latitude": 37.7749,
      "longitude": -122.4194,
      "accuracy": 10.5,
      "speed": 5.2,
      "heading": 180.0,
      "timestamp": "2024-01-01T10:05:00.000Z"
    }
  ],
  "total": 25
}
```

---

## Location Sharing Endpoints

### 6. Start Location Sharing
**POST** `/sharing/start`

Create a shareable link for location tracking.

**Request Body:**
```json
{
  "sessionId": "parcel_123_1640995200000",
  "toUserId": 789,
  "title": "Delivery in Progress",
  "message": "Track my delivery progress",
  "isPublic": false,
  "expiresAt": "2024-01-01T12:00:00.000Z",
  "maxViews": 100
}
```

**Response:**
```json
{
  "status": true,
  "message": "Location sharing started successfully",
  "share": {
    "shareId": "uuid-share-id",
    "shareUrl": "http://localhost:3000/shared-location/uuid-share-id",
    "expiresAt": "2024-01-01T12:00:00.000Z",
    "isPublic": false
  }
}
```

### 7. Stop Location Sharing
**POST** `/sharing/stop`

Stop sharing a location.

**Request Body:**
```json
{
  "shareId": "uuid-share-id"
}
```

**Response:**
```json
{
  "status": true,
  "message": "Location sharing stopped successfully"
}
```

### 8. Get Shared Location
**GET** `/sharing/shared/:shareId`

Access a shared location (public endpoint).

**Response:**
```json
{
  "status": true,
  "share": {
    "shareId": "uuid-share-id",
    "title": "Delivery in Progress",
    "message": "Track my delivery progress",
    "fromUser": {
      "name": "John Doe"
    },
    "location": {
      "latitude": 37.7749,
      "longitude": -122.4194,
      "accuracy": 10.5,
      "lastUpdated": "2024-01-01T10:05:00.000Z"
    },
    "createdAt": "2024-01-01T10:00:00.000Z",
    "expiresAt": "2024-01-01T12:00:00.000Z"
  }
}
```

### 9. Get User's Shared Locations
**GET** `/sharing/my-shares`

Get all locations currently being shared by the user.

**Response:**
```json
{
  "status": true,
  "sharedLocations": [
    {
      "shareId": "uuid-share-id",
      "shareUrl": "http://localhost:3000/shared-location/uuid-share-id",
      "title": "Delivery in Progress",
      "toUser": {
        "id": 789,
        "name": "Jane Smith"
      },
      "isPublic": false,
      "viewCount": 5,
      "createdAt": "2024-01-01T10:00:00.000Z",
      "expiresAt": "2024-01-01T12:00:00.000Z"
    }
  ]
}
```

---

## Location Request Endpoints

### 10. Send Location Access Request
**POST** `/requests/send`

Request location access from another user.

**Request Body:**
```json
{
  "toUserId": 789,
  "message": "Can you share your location for delivery coordination?",
  "requestType": "parcel_delivery",
  "duration": 60
}
```

**Response:**
```json
{
  "status": true,
  "message": "Location access request sent successfully",
  "request": {
    "requestId": "uuid-request-id",
    "status": "pending",
    "expiresAt": "2024-01-02T10:00:00.000Z"
  }
}
```

### 11. Respond to Location Request
**POST** `/requests/respond`

Accept or decline a location access request.

**Request Body:**
```json
{
  "requestId": "uuid-request-id",
  "response": "accepted"
}
```

**Response:**
```json
{
  "status": true,
  "message": "Location request accepted successfully",
  "shareId": "uuid-share-id"
}
```

### 12. Get Location Requests
**GET** `/requests?status=pending`

Get location requests received by the user.

**Response:**
```json
{
  "status": true,
  "requests": [
    {
      "requestId": "uuid-request-id",
      "fromUser": {
        "id": 456,
        "name": "John Doe"
      },
      "message": "Can you share your location for delivery coordination?",
      "requestType": "parcel_delivery",
      "duration": 60,
      "status": "pending",
      "createdAt": "2024-01-01T10:00:00.000Z",
      "expiresAt": "2024-01-02T10:00:00.000Z",
      "respondedAt": null
    }
  ]
}
```

---

## Enhanced Parcel Acceptance

The parcel acceptance endpoint has been enhanced to include location tracking:

**POST** `/api/parcels/accept`

**Request Body:**
```json
{
  "parcel_id": 123,
  "accepted_location_lat": 37.7749,
  "accepted_location_lng": -122.4194
}
```

**Response includes:**
```json
{
  "status": true,
  "message": "Parcel accepted successfully",
  "acceptance": {
    "tracking_session_id": "parcel_123_1640995200000",
    "accepted_location_lat": 37.7749,
    "accepted_location_lng": -122.4194,
    // ... other acceptance fields
  }
}
```

---

## Error Responses

All endpoints return consistent error responses:

```json
{
  "status": false,
  "message": "Error description",
  "error": "Detailed error message (in development)"
}
```

Common HTTP status codes:
- `400` - Bad Request (missing/invalid parameters)
- `401` - Unauthorized (invalid/missing token)
- `403` - Forbidden (access denied)
- `404` - Not Found (resource not found)
- `500` - Internal Server Error

---

## Database Schema

### Tables Created:
1. **location_tracking** - Stores location tracking data
2. **location_sharing** - Manages location sharing sessions
3. **location_requests** - Handles location access requests

### Enhanced Tables:
1. **parcel_acceptance** - Added location and tracking fields

---

## Installation & Setup

1. Install new dependencies:
```bash
npm install socket.io uuid
```

2. Run database migrations:
```bash
npx sequelize-cli db:migrate
```

3. Start the server with WebSocket support:
```bash
npm start
```

The API is now ready for live location tracking with real-time updates! 🚀
