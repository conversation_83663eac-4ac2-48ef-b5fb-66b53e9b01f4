{"name": "sendnow-api", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "nodemon server.js"}, "engines": {"node": ">=16.6.1 <24"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"body-parser": "^1.20.2", "cors": "^2.8.5", "cron": "^2.3.1", "crypto": "^1.0.1", "dotenv": "^16.4.5", "express": "^4.19.2", "express-session": "^1.17.4", "formidable": "^3.5.1", "https": "^1.0.0", "jsonwebtoken": "^9.0.2", "md5": "^2.3.0", "moment": "^2.30.1", "moment-timezone": "^0.5.44", "multer": "^1.4.5-lts.1", "mysql2": "^3.9.10", "node-fetch": "^3.3.2", "node-localstorage": "3.0.5", "nodemailer": "^6.9.11", "nodemon": "^3.1.0", "os": "^0.1.2", "otp-generator": "4.0.1", "passport": "^0.7.0", "passport-google-oauth2": "^0.2.0", "path": "^0.12.7", "sendmail": "^1.6.1", "sequelize": "6.37.5", "socket.io": "^4.7.5", "uuid": "^9.0.1"}, "devDependencies": {"sequelize-cli": "^6.6.3"}}